import React from 'react';
import { SearchableSelect } from '@/components/ui/searchable-select';

interface PropertySelectorProps {
  value: string;
  onChange: (value: string) => void;
  options: Array<{ label: string; value: string }>;
  disabled?: boolean;
}

export const PropertySelector: React.FC<PropertySelectorProps> = ({
  value,
  onChange,
  options,
  disabled = false,
}) => {
  const getOptionsWithDefault = () => {
    if (options.length > 0) {
      const hasCurrentValue = options.some((option) => option.value === value);
      if (hasCurrentValue) {
        return options;
      }

      if (value && value !== '') {
        return [{ label: value, value: value }, ...options];
      }
      return options;
    }

    if (value && value !== '') {
      return [{ label: value, value: value }];
    }
    return [{ label: 'No properties found', value: 'No properties found' }];
  };

  return (
    <div className="flex items-center space-x-1 flex-shrink-0">
      <span className="text-gray-600 hidden xl:inline text-xs">Property:</span>
      <SearchableSelect
        options={getOptionsWithDefault()}
        value={value}
        onChange={onChange}
        placeholder="Property"
        className="w-36 sm:w-44 lg:w-52 h-7"
        disabled={disabled || options.length === 0}
      />
    </div>
  );
};
