import React from 'react';
import { SearchableSelect } from '@/components/ui/searchable-select';

interface SystemSelectorProps {
  value: string;
  onChange: (value: string) => void;
  options: Array<{ label: string; value: string }>;
  disabled?: boolean;
}

export const SystemSelector: React.FC<SystemSelectorProps> = ({
  value,
  onChange,
  options,
  disabled = false,
}) => {
  const getOptionsWithDefault = () => {
    if (options.length > 0) {
      const hasCurrentValue = options.some((option) => option.value === value);
      if (hasCurrentValue) {
        return options;
      }

      if (value && value !== '') {
        return [{ label: value, value: value }, ...options];
      }
      return options;
    }

    if (value && value !== '') {
      return [{ label: value, value: value }];
    }
    return [{ label: 'No systems found', value: 'No systems found' }];
  };

  return (
    <div className="flex items-center space-x-1 flex-shrink-0">
      <span className="text-gray-600 hidden xl:inline text-xs">System:</span>
      <SearchableSelect
        options={getOptionsWithDefault()}
        value={value}
        onChange={onChange}
        placeholder="System"
        className="w-48 sm:w-56 lg:w-64 h-7"
        disabled={disabled || options.length === 0}
      />
    </div>
  );
};
