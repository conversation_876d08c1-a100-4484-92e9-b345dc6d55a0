import { RootState } from '@/store';
import { useSelector } from 'react-redux';
import { FONT_SIZES, RESPONSIVE_FONTS } from '../constants/typography';
import LoadingSkeleton from './shared/LoadingSkeleton';

const trimSubmarket = (submarket: string): string => {
  if (!submarket || submarket === 'N/A') return submarket;
  const firstDashIndex = submarket.indexOf(',');
  return firstDashIndex !== -1
    ? submarket.substring(0, firstDashIndex)
    : submarket;
};

const formatDate = (dateString: string): string => {
  if (!dateString) return '';
  const date = new Date(dateString);
  if (isNaN(date.getTime())) return dateString;
  return date.toLocaleDateString('en-US', {
    month: '2-digit',
    day: '2-digit',
    year: '2-digit',
  });
};

const PropertyInfo = () => {
  const { propertyInfo, loadingStates } = useSelector(
    (state: RootState) => state.scorecard,
  );
  const isLoading = loadingStates.propertyInfo;

  return (
    <div
      className="text-white h-full flex flex-col"
      style={{ backgroundColor: '#6B7280' }}
    >
      {/* Logo */}
      <div className="p-2 flex-shrink-0 flex justify-center">
        <img
          src="/media/logo/logo1-dark.png"
          alt="Willow Bridge Logo"
          className="w-26 sm:w-30 h-auto"
          onLoad={() => {
            console.log('Logo loaded successfully');
          }}
          onError={() => {
            console.warn('Logo failed to load');
          }}
        />
      </div>

      {isLoading ? (
        <div className="flex-1 overflow-hidden px-2 pb-2">
          <LoadingSkeleton type="property-info" />
        </div>
      ) : (
        <>
          {/* Property Name */}
          <h1
            className={`${RESPONSIVE_FONTS.pageTitle} font-bold text-white mb-2 text-center px-2 flex-shrink-0`}
          >
            {propertyInfo.propertyName || ''}
          </h1>

          {/* Property Image */}
          <div className="px-2 mb-2 flex-shrink-0">
            {propertyInfo.imageUrl ? (
              <img
                src={propertyInfo.imageUrl}
                alt="Property"
                className="w-full h-24 sm:h-32 object-contain"
                onError={(e) => {
                  console.warn(
                    'Property image failed to load:',
                    propertyInfo.imageUrl,
                  );
                  e.currentTarget.style.display = 'none';
                }}
              />
            ) : (
              <div className="w-full h-16 sm:h-20 bg-gray-600 flex items-center justify-center">
                <span className={`text-gray-300 ${FONT_SIZES.tiny}`}>
                  No Image Available
                </span>
              </div>
            )}
          </div>

          {/* Property Details - scrollable */}
          <div className="flex-1 overflow-y-auto px-2 pb-2 min-h-0">
            <div
              className={`space-y-1 text-white ${RESPONSIVE_FONTS.propertyDetails}`}
            >
              <div className="flex justify-between items-center">
                <span className="text-white">Property Code :</span>
                <span className="font-semibold text-right">
                  {propertyInfo.propertyCode}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-white">Address :</span>
                <span className="font-semibold text-right break-words ml-2">
                  {propertyInfo.address}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-white">City :</span>
                <span className="font-semibold text-right">
                  {propertyInfo.city}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-white">State :</span>
                <span className="font-semibold text-right">
                  {propertyInfo.state}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-white">ZIP Code :</span>
                <span className="font-semibold text-right">
                  {propertyInfo.zipCode}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-white">Submarket :</span>
                <span className="font-semibold text-right">
                  {trimSubmarket(propertyInfo.submarket)}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-white">Units :</span>
                <span className="font-semibold text-right">
                  {propertyInfo.units}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-white">Beg of Ops :</span>
                <span className="font-semibold text-right">
                  {formatDate(propertyInfo.begOfOps)}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-white">Asset Type :</span>
                <span className="font-semibold text-right">
                  {propertyInfo.assetType}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-white">Year Built :</span>
                <span className="font-semibold text-right">
                  {propertyInfo.yearBuilt}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-white">Residential Sqft :</span>
                <span className="font-semibold text-right">
                  {propertyInfo.residentialSqft.toLocaleString('en-US', {
                    maximumFractionDigits: 0,
                  })}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-white">Avg Res Unit Size :</span>
                <span className="font-semibold text-right">
                  {propertyInfo.avgResUnitSize.toLocaleString('en-US', {
                    maximumFractionDigits: 0,
                  })}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-white">Retail Sqft :</span>
                <span className="font-semibold text-right">
                  {propertyInfo.retailSqft}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-white">Retail Spaces :</span>
                <span className="font-semibold text-right">
                  {propertyInfo.retailSpaces}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-white">Asset Class :</span>
                <span className="font-semibold text-right">
                  {propertyInfo.assetClass}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-white">Region :</span>
                <span className="font-semibold text-right">
                  {propertyInfo.region}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-white">SVP :</span>
                <span className="font-semibold text-right">
                  {propertyInfo.svp || ''}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-white">RVP :</span>
                <span className="font-semibold text-right">
                  {propertyInfo.rvp}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-white">VP :</span>
                <span className="font-semibold text-right">
                  {propertyInfo.vp}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-white">RPM :</span>
                <span className="font-semibold text-right">
                  {propertyInfo.rpm}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-white">Affordable Units :</span>
                <span className="font-semibold text-right">
                  {propertyInfo.renewableUnits}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-white">Non-Revenue Units :</span>
                <span className="font-semibold text-right">
                  {propertyInfo.nonRevenueUnits}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-white">Down Units :</span>
                <span className="font-semibold text-right">
                  {propertyInfo.downUnits}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-white">Property Strategy :</span>
                <span className="font-semibold text-right">
                  {propertyInfo.propertyStrategy}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-white">Same Store :</span>
                <span className="font-semibold text-right">
                  {propertyInfo.sameStore}
                </span>
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default PropertyInfo;
